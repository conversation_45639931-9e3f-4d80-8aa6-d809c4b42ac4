"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { LoadingSpinner } from "@/components/loading-spinner"

export function NavigationLoading() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const handleRouteChangeStart = () => {
      timeoutId = setTimeout(() => {
        setIsLoading(true)
      }, 1000)
    }

    const handleRouteChangeComplete = () => {
      clearTimeout(timeoutId)
      setIsLoading(false)
    }

    // Listen for programmatic navigation
    const originalPush = router.push
    const originalReplace = router.replace

    router.push = (...args) => {
      handleRouteChangeStart()
      return originalPush.apply(router, args).finally(() => {
        handleRouteChangeComplete()
      })
    }

    router.replace = (...args) => {
      handleRouteChangeStart()
      return originalReplace.apply(router, args).finally(() => {
        handleRouteChangeComplete()
      })
    }

    // Cleanup
    return () => {
      clearTimeout(timeoutId)
      router.push = originalPush
      router.replace = originalReplace
    }
  }, [router])

  if (!isLoading) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b">
      <div className="container mx-auto px-4 py-2 flex items-center justify-center gap-2">
        <LoadingSpinner size="sm" />
        <span className="text-sm text-muted-foreground">Loading...</span>
      </div>
    </div>
  )
}
