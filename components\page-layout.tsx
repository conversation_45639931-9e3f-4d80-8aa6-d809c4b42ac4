"use client"

import type React from "react"

import { useAuth } from "@/components/auth-provider"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface PageLayoutProps {
  children: React.ReactNode
  title: string
  showNavigation?: boolean
}

export function PageLayout({ children, title, showNavigation = true }: PageLayoutProps) {
  const { user, signOut } = useAuth()
  const router = useRouter()

  const handleSignOut = () => {
    signOut()
    router.push("/")
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      {showNavigation && (
        <nav className="border-b bg-card">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <Link href="/" className="text-xl font-semibold text-foreground">
                St Cloud Enterprises Portal
              </Link>

              {user && (
                <div className="flex items-center gap-4">
                  <Link href="/account">
                    <Button variant="ghost">Account</Button>
                  </Link>
                  <Link href="/dashboard">
                    <Button variant="ghost">Dashboard</Button>
                  </Link>
                  <Button variant="outline" onClick={handleSignOut}>
                    Sign Out
                  </Button>
                </div>
              )}
            </div>
          </div>
        </nav>
      )}

      {/* Page Title */}
      <div className="container mx-auto px-4 py-6">
        <h1 className="text-2xl font-bold text-foreground mb-6">{title}</h1>
        {children}
      </div>

      {/* Footer */}
      <footer className="mt-auto border-t bg-card">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center space-y-4">
            <h3 className="text-lg font-semibold">St Cloud Enterprises Portal</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Managing access to other website properties managed by St Cloud Enterprises, providing centralized
              authentication and property management solutions.
            </p>
            <div className="flex justify-center gap-6 text-sm">
              <Link href="/privacy" className="text-muted-foreground hover:text-foreground">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-muted-foreground hover:text-foreground">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
